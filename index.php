<?php
// URL to Markdown Converter with AJAX Processing
// PHP 8 implementation with detailed logging and progress tracking

class UrlToMarkdownConverter {
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
    private $logFile;
    
    public function __construct() {
        $this->logFile = sys_get_temp_dir() . '/conversion_log_' . session_id() . '.json';
    }
    
    private function log($step, $message, $progress = 0, $timeElapsed = 0, $memoryUsage = 0) {
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        $memoryLimit = $this->getMemoryLimit();
        $memoryPercent = ($currentMemory / $memoryLimit) * 100;
        
        // Performance warnings
        $warnings = [];
        if ($memoryPercent > 80) {
            $warnings[] = 'HIGH_MEMORY';
        }
        if ($timeElapsed > 5) {
            $warnings[] = 'SLOW_OPERATION';
        }
        
        $logEntry = [
            'timestamp' => microtime(true),
            'step' => $step,
            'message' => $message,
            'progress' => $progress,
            'time_elapsed' => $this->formatTime($timeElapsed),
            'time_elapsed_raw' => $timeElapsed,
            'memory_current' => $this->formatBytes($currentMemory),
            'memory_current_raw' => $currentMemory,
            'memory_peak' => $this->formatBytes($peakMemory),
            'memory_peak_raw' => $peakMemory,
            'memory_limit' => $this->formatBytes($memoryLimit),
            'memory_percent' => round($memoryPercent, 2),
            'warnings' => $warnings
        ];
        
        $logs = [];
        if (file_exists($this->logFile)) {
            $logs = json_decode(file_get_contents($this->logFile), true) ?: [];
        }
        
        $logs[] = $logEntry;
        file_put_contents($this->logFile, json_encode($logs));
    }
    
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    private function formatTime($seconds) {
        if ($seconds < 0.001) {
            return round($seconds * 1000000, 2) . ' μs';
        } elseif ($seconds < 1) {
            return round($seconds * 1000, 2) . ' ms';
        } else {
            return round($seconds, 3) . ' s';
        }
    }
    
    public function validateUrl($url) {
        $startTime = microtime(true);
        $this->log('validation', 'Starting URL validation...', 5);
        
        if (empty($url)) {
            $this->log('validation', 'Error: URL is empty', 5);
            throw new Exception('URL cannot be empty');
        }
        
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            $this->log('validation', 'Error: Invalid URL format', 5);
            throw new Exception('Invalid URL format provided');
        }
        
        $timeElapsed = microtime(true) - $startTime;
        $this->log('validation', 'URL validation completed successfully', 10, $timeElapsed, memory_get_usage(true));
        
        return true;
    }
    
    public function fetchContent($url) {
        $startTime = microtime(true);
        $this->log('fetch', 'Starting content fetch from URL...', 15);
        
        // Create context for file_get_contents
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: ' . $this->userAgent,
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language: en-US,en;q=0.5',
                    'Accept-Encoding: gzip, deflate',
                    'Connection: keep-alive',
                ],
                'timeout' => 30,
                'follow_location' => true,
                'max_redirects' => 5
            ]
        ]);
        
        $this->log('fetch', 'HTTP context created, initiating request...', 20);
        
        $content = @file_get_contents($url, false, $context);
        
        if ($content === false) {
            $this->log('fetch', 'Error: Failed to fetch content from URL', 20);
            throw new Exception('Failed to fetch content from URL. Please check if the URL is accessible.');
        }
        
        $contentSize = strlen($content);
        $timeElapsed = microtime(true) - $startTime;
        $this->log('fetch', "Content fetched successfully. Size: {$contentSize} bytes", 30, $timeElapsed, memory_get_usage(true));
        
        return $content;
    }
    
    public function processHtmlStep($html, $step) {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // Check memory usage and implement chunking for large content
        $contentSize = strlen($html);
        $memoryLimit = $this->getMemoryLimit();
        $availableMemory = $memoryLimit - memory_get_usage();
        
        $stepProgress = [
            'cleanup' => ['start' => 35, 'end' => 45, 'desc' => 'Removing scripts and styles'],
            'headers' => ['start' => 45, 'end' => 55, 'desc' => 'Converting headers'],
            'paragraphs' => ['start' => 55, 'end' => 60, 'desc' => 'Converting paragraphs'],
            'formatting' => ['start' => 60, 'end' => 70, 'desc' => 'Converting bold/italic formatting'],
            'links' => ['start' => 70, 'end' => 75, 'desc' => 'Converting links'],
            'images' => ['start' => 75, 'end' => 80, 'desc' => 'Converting images'],
            'lists' => ['start' => 80, 'end' => 85, 'desc' => 'Converting lists'],
            'code' => ['start' => 85, 'end' => 90, 'desc' => 'Converting code blocks'],
            'quotes' => ['start' => 90, 'end' => 92, 'desc' => 'Converting blockquotes'],
            'final' => ['start' => 92, 'end' => 100, 'desc' => 'Final cleanup']
        ];
        
        if (!isset($stepProgress[$step])) {
            throw new Exception("Unknown processing step: {$step}");
        }
        
        $stepInfo = $stepProgress[$step];
        $this->log('processing', "Starting: {$stepInfo['desc']} (Content size: " . number_format($contentSize) . " bytes)", $stepInfo['start']);
        
        // Use chunked processing for large content (> 1MB)
        if ($contentSize > 1048576) {
            $this->log('processing', "Large content detected, using chunked processing...", $stepInfo['start'] + 0.5);
            return $this->processHtmlStepChunked($html, $step, $stepInfo);
        }
        
        switch ($step) {
            case 'cleanup':
                $this->log('processing', "Removing script tags...", $stepInfo['start'] + 1);
                $html = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html);
                
                $this->log('processing', "Removing style tags...", $stepInfo['start'] + 2);
                $html = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html);
                
                $this->log('processing', "Removing comments...", $stepInfo['start'] + 3);
                $html = preg_replace('/<!--.*?-->/s', '', $html);
                break;
                
            case 'headers':
                $this->log('processing', "Converting H1 headers...", $stepInfo['start'] + 1);
                $html = preg_replace('/<h1[^>]*>(.*?)<\/h1>/is', '# $1\n\n', $html);
                
                $this->log('processing', "Converting H2 headers...", $stepInfo['start'] + 2);
                $html = preg_replace('/<h2[^>]*>(.*?)<\/h2>/is', '## $1\n\n', $html);
                
                $this->log('processing', "Converting H3 headers...", $stepInfo['start'] + 3);
                $html = preg_replace('/<h3[^>]*>(.*?)<\/h3>/is', '### $1\n\n', $html);
                
                $this->log('processing', "Converting H4-H6 headers...", $stepInfo['start'] + 4);
                $html = preg_replace('/<h4[^>]*>(.*?)<\/h4>/is', '#### $1\n\n', $html);
                $html = preg_replace('/<h5[^>]*>(.*?)<\/h5>/is', '##### $1\n\n', $html);
                $html = preg_replace('/<h6[^>]*>(.*?)<\/h6>/is', '###### $1\n\n', $html);
                break;
                
            case 'paragraphs':
                $this->log('processing', "Converting paragraph tags...", $stepInfo['start'] + 1);
                $html = preg_replace('/<p[^>]*>(.*?)<\/p>/is', '$1\n\n', $html);
                
                $this->log('processing', "Converting div tags...", $stepInfo['start'] + 2);
                $html = preg_replace('/<div[^>]*>(.*?)<\/div>/is', '$1\n', $html);
                
                $this->log('processing', "Converting line breaks...", $stepInfo['start'] + 3);
                $html = preg_replace('/<br[^>]*>/i', '\n', $html);
                break;
                
            case 'formatting':
                $this->log('processing', "Converting bold text...", $stepInfo['start'] + 1);
                $html = preg_replace('/<strong[^>]*>(.*?)<\/strong>/is', '**$1**', $html);
                $html = preg_replace('/<b[^>]*>(.*?)<\/b>/is', '**$1**', $html);
                
                $this->log('processing', "Converting italic text...", $stepInfo['start'] + 2);
                $html = preg_replace('/<em[^>]*>(.*?)<\/em>/is', '*$1*', $html);
                $html = preg_replace('/<i[^>]*>(.*?)<\/i>/is', '*$1*', $html);
                
                $this->log('processing', "Converting underlined text...", $stepInfo['start'] + 3);
                $html = preg_replace('/<u[^>]*>(.*?)<\/u>/is', '_$1_', $html);
                break;
                
            case 'links':
                $this->log('processing', "Converting hyperlinks...", $stepInfo['start'] + 1);
                $html = preg_replace('/<a[^>]*href=["\']([^"\'>]*)["\'][^>]*>(.*?)<\/a>/is', '[$2]($1)', $html);
                
                $this->log('processing', "Cleaning up empty links...", $stepInfo['start'] + 2);
                $html = preg_replace('/\[\]\([^)]*\)/', '', $html);
                break;
                
            case 'images':
                $this->log('processing', "Converting images with alt text...", $stepInfo['start'] + 1);
                $html = preg_replace('/<img[^>]*src=["\']([^"\'>]*)["\'][^>]*alt=["\']([^"\'>]*)["\'][^>]*>/is', '![$2]($1)', $html);
                $html = preg_replace('/<img[^>]*alt=["\']([^"\'>]*)["\'][^>]*src=["\']([^"\'>]*)["\'][^>]*>/is', '![$1]($2)', $html);
                
                $this->log('processing', "Converting images without alt text...", $stepInfo['start'] + 2);
                $html = preg_replace('/<img[^>]*src=["\']([^"\'>]*)["\'][^>]*>/is', '![]($1)', $html);
                break;
                
            case 'lists':
                $this->log('processing', "Converting unordered lists...", $stepInfo['start'] + 1);
                $html = preg_replace('/<ul[^>]*>/i', '', $html);
                $html = preg_replace('/<\/ul>/i', '\n', $html);
                
                $this->log('processing', "Converting ordered lists...", $stepInfo['start'] + 2);
                $html = preg_replace('/<ol[^>]*>/i', '', $html);
                $html = preg_replace('/<\/ol>/i', '\n', $html);
                
                $this->log('processing', "Converting list items...", $stepInfo['start'] + 3);
                $html = preg_replace('/<li[^>]*>(.*?)<\/li>/is', '- $1\n', $html);
                break;
                
            case 'code':
                $this->log('processing', "Converting code blocks...", $stepInfo['start'] + 1);
                $html = preg_replace('/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/is', '```\n$1\n```\n\n', $html);
                
                $this->log('processing', "Converting inline code...", $stepInfo['start'] + 2);
                $html = preg_replace('/<code[^>]*>(.*?)<\/code>/is', '`$1`', $html);
                
                $this->log('processing', "Converting pre tags...", $stepInfo['start'] + 3);
                $html = preg_replace('/<pre[^>]*>(.*?)<\/pre>/is', '```\n$1\n```\n\n', $html);
                break;
                
            case 'quotes':
                $this->log('processing', "Converting blockquotes...", $stepInfo['start'] + 1);
                $html = preg_replace('/<blockquote[^>]*>(.*?)<\/blockquote>/is', '> $1\n\n', $html);
                break;
                
            case 'final':
                $this->log('processing', "Removing remaining HTML tags...", $stepInfo['start'] + 1);
                $html = strip_tags($html);
                
                $this->log('processing', "Decoding HTML entities...", $stepInfo['start'] + 2);
                $html = html_entity_decode($html, ENT_QUOTES, 'UTF-8');
                
                $this->log('processing', "Cleaning up whitespace...", $stepInfo['start'] + 3);
                $html = preg_replace('/\n{3,}/', '\n\n', $html);
                $html = preg_replace('/[ \t]+/', ' ', $html);
                $html = trim($html);
                
                $this->log('processing', "Final formatting...", $stepInfo['start'] + 4);
                // Ensure proper spacing around headers
                $html = preg_replace('/\n(#{1,6}\s)/', '\n\n$1', $html);
                $html = preg_replace('/(#{1,6}\s[^\n]+)\n(?!\n)/', '$1\n\n', $html);
                break;
        }
        
        $timeElapsed = microtime(true) - $startTime;
        $memoryUsed = memory_get_usage() - $startMemory;
        $this->log('processing', "Completed: {$stepInfo['desc']}", $stepInfo['end'], $timeElapsed, memory_get_usage(true));
        
        return $html;
    }
    
    public function getLogs() {
        if (file_exists($this->logFile)) {
            return json_decode(file_get_contents($this->logFile), true) ?: [];
        }
        return [];
    }
    
    public function clearLogs() {
        if (file_exists($this->logFile)) {
            unlink($this->logFile);
        }
    }
    
    private function getMemoryLimit() {
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit == -1) {
            return PHP_INT_MAX; // No limit
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) $memoryLimit;
        
        switch ($unit) {
            case 'g': return $value * 1024 * 1024 * 1024;
            case 'm': return $value * 1024 * 1024;
            case 'k': return $value * 1024;
            default: return $value;
        }
    }
    
    private function processHtmlStepChunked($html, $step, $stepInfo) {
        $startTime = microtime(true);
        $chunkSize = 524288; // 512KB chunks
        $htmlLength = strlen($html);
        $chunks = ceil($htmlLength / $chunkSize);
        
        $this->log('processing', "Processing in {$chunks} chunks of " . number_format($chunkSize) . " bytes each", $stepInfo['start'] + 1);
        
        $result = '';
        $progressStep = ($stepInfo['end'] - $stepInfo['start']) / $chunks;
        
        for ($i = 0; $i < $chunks; $i++) {
            $chunkStart = $i * $chunkSize;
            $chunk = substr($html, $chunkStart, $chunkSize);
            
            // Ensure we don't break HTML tags at chunk boundaries
            if ($i < $chunks - 1) {
                $lastTagPos = strrpos($chunk, '<');
                $lastClosePos = strrpos($chunk, '>');
                
                if ($lastTagPos !== false && $lastTagPos > $lastClosePos) {
                    // We have an incomplete tag, adjust chunk size
                    $chunk = substr($chunk, 0, $lastTagPos);
                    $chunkSize = strlen($chunk);
                }
            }
            
            $currentProgress = $stepInfo['start'] + ($i + 1) * $progressStep;
            $this->log('processing', "Processing chunk " . ($i + 1) . "/{$chunks}...", $currentProgress);
            
            // Process this chunk
            $processedChunk = $this->processSingleChunk($chunk, $step);
            $result .= $processedChunk;
            
            // Memory cleanup
            unset($chunk, $processedChunk);
            
            // Check memory usage
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->getMemoryLimit();
            
            if ($memoryUsage > ($memoryLimit * 0.8)) {
                $this->log('processing', "High memory usage detected: " . number_format($memoryUsage / 1024 / 1024, 2) . "MB", $currentProgress + 0.1);
                // Force garbage collection
                gc_collect_cycles();
            }
        }
        
        $timeElapsed = microtime(true) - $startTime;
        $this->log('processing', "Completed chunked processing: {$stepInfo['desc']}", $stepInfo['end'], $timeElapsed, memory_get_usage(true));
        
        return $result;
    }
    
    private function processSingleChunk($chunk, $step) {
        switch ($step) {
            case 'cleanup':
                $chunk = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $chunk);
                $chunk = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $chunk);
                $chunk = preg_replace('/<!--.*?-->/s', '', $chunk);
                break;
                
            case 'headers':
                $chunk = preg_replace('/<h1[^>]*>(.*?)<\/h1>/is', '# $1\n\n', $chunk);
                $chunk = preg_replace('/<h2[^>]*>(.*?)<\/h2>/is', '## $1\n\n', $chunk);
                $chunk = preg_replace('/<h3[^>]*>(.*?)<\/h3>/is', '### $1\n\n', $chunk);
                $chunk = preg_replace('/<h4[^>]*>(.*?)<\/h4>/is', '#### $1\n\n', $chunk);
                $chunk = preg_replace('/<h5[^>]*>(.*?)<\/h5>/is', '##### $1\n\n', $chunk);
                $chunk = preg_replace('/<h6[^>]*>(.*?)<\/h6>/is', '###### $1\n\n', $chunk);
                break;
                
            case 'paragraphs':
                $chunk = preg_replace('/<p[^>]*>(.*?)<\/p>/is', '$1\n\n', $chunk);
                $chunk = preg_replace('/<div[^>]*>(.*?)<\/div>/is', '$1\n', $chunk);
                $chunk = preg_replace('/<br[^>]*>/i', '\n', $chunk);
                break;
                
            case 'formatting':
                $chunk = preg_replace('/<strong[^>]*>(.*?)<\/strong>/is', '**$1**', $chunk);
                $chunk = preg_replace('/<b[^>]*>(.*?)<\/b>/is', '**$1**', $chunk);
                $chunk = preg_replace('/<em[^>]*>(.*?)<\/em>/is', '*$1*', $chunk);
                $chunk = preg_replace('/<i[^>]*>(.*?)<\/i>/is', '*$1*', $chunk);
                $chunk = preg_replace('/<u[^>]*>(.*?)<\/u>/is', '_$1_', $chunk);
                break;
                
            case 'links':
                $chunk = preg_replace('/<a[^>]*href=["\']([^"\'>]*)["\'][^>]*>(.*?)<\/a>/is', '[$2]($1)', $chunk);
                $chunk = preg_replace('/\[\]\([^)]*\)/', '', $chunk);
                break;
                
            case 'images':
                $chunk = preg_replace('/<img[^>]*src=["\']([^"\'>]*)["\'][^>]*alt=["\']([^"\'>]*)["\'][^>]*>/is', '![$2]($1)', $chunk);
                $chunk = preg_replace('/<img[^>]*alt=["\']([^"\'>]*)["\'][^>]*src=["\']([^"\'>]*)["\'][^>]*>/is', '![$1]($2)', $chunk);
                $chunk = preg_replace('/<img[^>]*src=["\']([^"\'>]*)["\'][^>]*>/is', '![]($1)', $chunk);
                break;
                
            case 'lists':
                $chunk = preg_replace('/<ul[^>]*>/i', '', $chunk);
                $chunk = preg_replace('/<\/ul>/i', '\n', $chunk);
                $chunk = preg_replace('/<ol[^>]*>/i', '', $chunk);
                $chunk = preg_replace('/<\/ol>/i', '\n', $chunk);
                $chunk = preg_replace('/<li[^>]*>(.*?)<\/li>/is', '- $1\n', $chunk);
                break;
                
            case 'code':
                $chunk = preg_replace('/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/is', '```\n$1\n```\n\n', $chunk);
                $chunk = preg_replace('/<code[^>]*>(.*?)<\/code>/is', '`$1`', $chunk);
                $chunk = preg_replace('/<pre[^>]*>(.*?)<\/pre>/is', '```\n$1\n```\n\n', $chunk);
                break;
                
            case 'quotes':
                $chunk = preg_replace('/<blockquote[^>]*>(.*?)<\/blockquote>/is', '> $1\n\n', $chunk);
                break;
                
            case 'final':
                $chunk = strip_tags($chunk);
                $chunk = html_entity_decode($chunk, ENT_QUOTES, 'UTF-8');
                $chunk = preg_replace('/\n{3,}/', '\n\n', $chunk);
                $chunk = preg_replace('/[ \t]+/', ' ', $chunk);
                $chunk = trim($chunk);
                break;
        }
        
        return $chunk;
    }
}

// Start session for logging
session_start();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    $converter = new UrlToMarkdownConverter();
    
    switch ($_GET['action']) {
        case 'validate':
            try {
                $url = trim($_POST['url'] ?? '');
                
                if (empty($url)) {
                    throw new Exception('URL is required and cannot be empty');
                }
                
                $converter->validateUrl($url);
                echo json_encode([
                    'success' => true, 
                    'message' => 'URL validation successful',
                    'url' => $url,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } catch (Exception $e) {
                $converter->log('validation', 'Validation failed: ' . $e->getMessage(), 0);
                echo json_encode([
                    'success' => false, 
                    'error' => $e->getMessage(),
                    'error_code' => 'VALIDATION_ERROR',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'suggestions' => [
                        'Ensure the URL starts with http:// or https://',
                        'Check for typos in the URL',
                        'Verify the domain name is valid'
                    ]
                ]);
            } catch (Throwable $e) {
                $converter->log('validation', 'Critical error during validation: ' . $e->getMessage(), 0);
                echo json_encode([
                    'success' => false,
                    'error' => 'A critical error occurred during validation',
                    'error_code' => 'CRITICAL_ERROR',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            exit;
            
        case 'fetch':
            try {
                $url = trim($_POST['url'] ?? '');
                
                if (empty($url)) {
                    throw new Exception('URL is required for content fetching');
                }
                
                $startTime = microtime(true);
                $content = $converter->fetchContent($url);
                $fetchTime = microtime(true) - $startTime;
                
                if (empty($content)) {
                    throw new Exception('The URL returned empty content');
                }
                
                // Store content in session for processing
                $_SESSION['html_content'] = $content;
                $_SESSION['original_url'] = $url;
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Content fetched successfully',
                    'content_size' => strlen($content),
                    'fetch_time' => round($fetchTime, 3),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } catch (Exception $e) {
                $converter->log('fetch', 'Fetch failed: ' . $e->getMessage(), 0);
                echo json_encode([
                    'success' => false, 
                    'error' => $e->getMessage(),
                    'error_code' => 'FETCH_ERROR',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'suggestions' => [
                        'Check if the URL is accessible in your browser',
                        'Verify the website is not blocking automated requests',
                        'Try again in a few moments if the server is busy',
                        'Ensure you have internet connectivity'
                    ]
                ]);
            } catch (Throwable $e) {
                $converter->log('fetch', 'Critical error during fetch: ' . $e->getMessage(), 0);
                echo json_encode([
                    'success' => false,
                    'error' => 'A critical error occurred while fetching content',
                    'error_code' => 'CRITICAL_ERROR',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            exit;
            
        case 'process':
            try {
                $step = $_POST['step'] ?? '';
                $html = $_SESSION['html_content'] ?? '';
                
                if (empty($step)) {
                    throw new Exception('Processing step is required');
                }
                
                if (empty($html)) {
                    throw new Exception('No content available for processing. Please fetch content first.');
                }
                
                $validSteps = ['cleanup', 'headers', 'paragraphs', 'formatting', 'links', 'images', 'lists', 'code', 'quotes', 'final'];
                if (!in_array($step, $validSteps)) {
                    throw new Exception('Invalid processing step: ' . $step);
                }
                
                $startTime = microtime(true);
                $result = $converter->processHtmlStep($html, $step);
                $processTime = microtime(true) - $startTime;
                
                $_SESSION['html_content'] = $result;
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Processing step completed successfully',
                    'step' => $step,
                    'content_size' => strlen($result),
                    'process_time' => round($processTime, 3),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } catch (Exception $e) {
                $converter->log('processing', 'Processing failed: ' . $e->getMessage(), 0);
                echo json_encode([
                    'success' => false, 
                    'error' => $e->getMessage(),
                    'error_code' => 'PROCESSING_ERROR',
                    'step' => $_POST['step'] ?? 'unknown',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'suggestions' => [
                        'Try refreshing the page and starting over',
                        'Check if the content is too large for processing',
                        'Verify the HTML content is valid'
                    ]
                ]);
            } catch (Throwable $e) {
                $converter->log('processing', 'Critical error during processing: ' . $e->getMessage(), 0);
                echo json_encode([
                    'success' => false,
                    'error' => 'A critical error occurred during processing',
                    'error_code' => 'CRITICAL_ERROR',
                    'step' => $_POST['step'] ?? 'unknown',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            exit;
            
        case 'logs':
            try {
                $logs = $converter->getLogs();
                echo json_encode([
                    'success' => true,
                    'logs' => $logs,
                    'count' => count($logs),
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to retrieve logs: ' . $e->getMessage(),
                    'error_code' => 'LOG_ERROR',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            exit;
            
        case 'download':
            try {
                $markdown = $_SESSION['html_content'] ?? '';
                if (empty($markdown)) {
                    throw new Exception('No content available for download. Please complete the conversion process first.');
                }
                
                $originalUrl = $_SESSION['original_url'] ?? 'unknown';
                $filename = 'converted_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', parse_url($originalUrl, PHP_URL_HOST) ?? 'content') . '_' . date('Y-m-d_H-i-s') . '.md';
                
                // Add metadata header to markdown
                $metadata = "<!-- Converted from: {$originalUrl} -->\n";
                $metadata .= "<!-- Conversion date: " . date('Y-m-d H:i:s') . " -->\n\n";
                $fullContent = $metadata . $markdown;
                
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Content-Length: ' . strlen($fullContent));
                header('Cache-Control: no-cache, must-revalidate');
                header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
                
                echo $fullContent;
                exit;
            } catch (Exception $e) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false, 
                    'error' => $e->getMessage(),
                    'error_code' => 'DOWNLOAD_ERROR',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'suggestions' => [
                        'Complete the conversion process first',
                        'Try the conversion again if it failed'
                    ]
                ]);
                exit;
            } catch (Throwable $e) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'A critical error occurred during download',
                    'error_code' => 'CRITICAL_ERROR',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                exit;
            }
            
        case 'clear':
            try {
                $converter->clearLogs();
                unset($_SESSION['html_content']);
                unset($_SESSION['original_url']);
                echo json_encode([
                    'success' => true,
                    'message' => 'Session cleared successfully',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to clear session: ' . $e->getMessage(),
                    'error_code' => 'CLEAR_ERROR',
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            exit;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action requested',
                'error_code' => 'INVALID_ACTION',
                'timestamp' => date('Y-m-d H:i:s'),
                'valid_actions' => ['validate', 'fetch', 'process', 'logs', 'download', 'clear']
            ]);
            exit;
    }
}

// Initialize variables for display
$markdown = '';
$error = '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL to Markdown Converter - Advanced</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .progress-bar {
            height: 30px;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        .markdown-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .markdown-raw, .markdown-rendered {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .markdown-raw {
            background: #f8f8f8;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .markdown-rendered {
            background: white;
        }
        .result-container {
            margin-top: 20px;
            display: none;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid #333;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #00ff00;
            padding-left: 10px;
        }
        
        .log-timestamp {
            color: #888;
            font-size: 12px;
        }
        
        .log-step {
            color: #ffff00;
            font-weight: bold;
        }
        
        .log-message {
            color: #00ff00;
        }
        
        .log-stats {
            color: #ff6600;
            font-size: 11px;
        }
        
        .error-log {
            border-left-color: #ff0000;
        }
        
        .error-log .log-message {
            color: #ff0000;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-idle { background-color: #gray; }
        .status-processing { background-color: #ffeb3b; animation: pulse 1s infinite; }
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .conversion-status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .memory-usage {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔄 Advanced URL to Markdown Converter</h1>
            <p>Convert any webpage to Markdown with detailed progress tracking and real-time logging</p>
        </header>

        <main>
            <form id="urlForm">
                <div class="input-group">
                    <input type="url" id="urlInput" placeholder="Enter URL to convert (e.g., https://example.com)" required>
                    <button type="submit" class="btn btn-primary" id="convertBtn">
                        <span class="status-indicator status-idle" id="statusIndicator"></span>
                        Convert to Markdown
                    </button>
                </div>
            </form>
            
            <div class="conversion-status" id="conversionStatus" style="display: none;">
                <h3>Conversion Progress</h3>
                <div class="progress-container" id="progressContainer">
                    <div class="progress-header">
                        <div class="progress-title" id="progressTitle">Processing...</div>
                        <div class="progress-percentage" id="progressPercentage">0%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Initializing...</div>
                    <div class="progress-steps" id="progressSteps">
                        <div class="progress-step" data-step="validate">
                            <div class="step-circle">1</div>
                            <span>Validate</span>
                        </div>
                        <div class="progress-step" data-step="fetch">
                            <div class="step-circle">2</div>
                            <span>Fetch</span>
                        </div>
                        <div class="progress-step" data-step="cleanup">
                            <div class="step-circle">3</div>
                            <span>Cleanup</span>
                        </div>
                        <div class="progress-step" data-step="headers">
                            <div class="step-circle">4</div>
                            <span>Headers</span>
                        </div>
                        <div class="progress-step" data-step="content">
                            <div class="step-circle">5</div>
                            <span>Content</span>
                        </div>
                        <div class="progress-step" data-step="format">
                            <div class="step-circle">6</div>
                            <span>Format</span>
                        </div>
                        <div class="progress-step" data-step="final">
                            <div class="step-circle">7</div>
                            <span>Final</span>
                        </div>
                    </div>
                </div>
                <div id="currentStep">Ready to start...</div>
                <div class="memory-usage" id="memoryUsage"></div>
            </div>
            
            <div class="controls">
                <button class="btn btn-success" id="downloadBtn" disabled>📥 Download Markdown</button>
                <button class="btn btn-danger" id="clearBtn">🗑️ Clear Logs</button>
                <button class="btn btn-primary" id="toggleLogsBtn">👁️ Toggle Logs</button>
            </div>
            
            <div class="log-container" id="logContainer" style="display: none;">
                <div class="log-header">
                    <span><span class="status-indicator processing" id="logStatus"></span>Processing Log</span>
                    <button onclick="converter.clearLogs()" style="background: #666; padding: 4px 8px; font-size: 12px;">Clear</button>
                </div>
                <div class="log-content" id="logEntries"></div>
            </div>

            <div class="result-section">
                <div class="tabs">
                    <button class="tab-button active" data-tab="raw">📝 Raw Markdown</button>
                    <button class="tab-button" data-tab="preview">👁️ Preview</button>
                </div>

                <div class="tab-content">
                    <div id="raw-tab" class="tab-pane active">
                        <textarea id="markdownOutput" placeholder="Converted Markdown will appear here..."></textarea>
                    </div>
                    <div id="preview-tab" class="tab-pane">
                        <div id="markdownPreview">Preview will appear here after conversion...</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        class UrlToMarkdownConverter {
            constructor() {
                this.isProcessing = false;
                this.currentStep = 0;
                this.totalSteps = 10;
                this.processingSteps = [
                    'cleanup', 'headers', 'paragraphs', 'formatting', 
                    'links', 'images', 'lists', 'code', 'quotes', 'final'
                ];
                this.initializeEventListeners();
                this.logPollingInterval = null;
            }
            
            addLogEntry(message, type = 'info') {
                const logContent = document.getElementById('logEntries');
                const logStatus = document.getElementById('logStatus');
                
                if (logContent) {
                    const entry = document.createElement('div');
                    entry.className = `log-entry ${type}`;
                    const timestamp = new Date().toLocaleTimeString();
                    entry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span>${message}`;
                    logContent.appendChild(entry);
                    logContent.scrollTop = logContent.scrollHeight;
                }
                
                // Update status indicator
                if (logStatus) {
                    logStatus.className = `status-indicator ${type === 'error' ? 'error' : type === 'success' ? 'success' : 'processing'}`;
                }
            }
            
            initializeEventListeners() {
                document.getElementById('urlForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.startConversion();
                });
                
                document.getElementById('downloadBtn').addEventListener('click', () => {
                    this.downloadMarkdown();
                });
                
                document.getElementById('clearBtn').addEventListener('click', () => {
                    this.clearLogs();
                });
                
                document.getElementById('toggleLogsBtn').addEventListener('click', () => {
                    this.toggleLogs();
                });
                
                // Tab switching
                document.querySelectorAll('.tab-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.switchTab(e.target.dataset.tab);
                    });
                });
            }
            
            async startConversion() {
                if (this.isProcessing) return;
                
                const url = document.getElementById('urlInput').value.trim();
                if (!url) {
                    alert('Please enter a URL');
                    return;
                }
                
                this.isProcessing = true;
                this.updateUI('processing');
                this.showConversionStatus();
                this.startLogPolling();
                
                try {
                    // Step 1: Validate URL
                    this.addLogEntry('🚀 Starting conversion process...', 'info');
                    this.updateProgress(5, 'Validating URL format and accessibility...', 'validate');
                    this.addLogEntry('🔍 Validating URL format and accessibility...', 'info');
                    await this.validateUrl(url);
                    this.addLogEntry('✅ URL validation successful', 'success');
                    
                    // Step 2: Fetch content
                    this.updateProgress(15, 'Fetching webpage content...', 'fetch');
                    this.addLogEntry('📥 Fetching webpage content...', 'info');
                    await this.fetchContent(url);
                    this.addLogEntry('✅ Content fetched successfully', 'success');
                    
                    // Step 3-12: Process HTML in steps
                    const stepEmojis = {
                        'cleanup': '🧹',
                        'headers': '📝',
                        'paragraphs': '📄',
                        'formatting': '✨',
                        'links': '🔗',
                        'images': '🖼️',
                        'lists': '📋',
                        'code': '💻',
                        'quotes': '💬',
                        'final': '🎯'
                    };
                    
                    for (let i = 0; i < this.processingSteps.length; i++) {
                        const step = this.processingSteps[i];
                        const progress = 20 + (i / this.processingSteps.length) * 70;
                        const emoji = stepEmojis[step] || '⚙️';
                        
                        this.updateProgress(progress, `Processing ${step} elements...`, step);
                        this.addLogEntry(`${emoji} Processing ${step} elements...`, 'processing');
                        
                        await this.processStep(step);
                        
                        this.addLogEntry(`✅ Completed ${step} processing`, 'success');
                        await this.sleep(150); // Small delay for UI updates
                    }
                    
                    // Get final result
                    const finalResult = await this.getFinalResult();
                    this.displayResult(finalResult);
                    
                    this.updateProgress(100, 'Conversion complete!', 'final');
                    this.addLogEntry('🎉 Conversion completed successfully!', 'success');
                    
                    this.updateUI('success');
                    document.getElementById('downloadBtn').disabled = false;
                    
                    // Update final status
                    const logStatus = document.getElementById('logStatus');
                    if (logStatus) {
                        logStatus.className = 'status-indicator success';
                    }
                    
                } catch (error) {
                    this.addLogEntry(`❌ Error: ${error.message}`, 'error');
                    this.updateProgress(0, 'Conversion failed');
                    this.updateUI('error');
                    this.showError(error.message);
                    
                    // Update error status
                    const logStatus = document.getElementById('logStatus');
                    if (logStatus) {
                        logStatus.className = 'status-indicator error';
                    }
                } finally {
                    this.isProcessing = false;
                    this.stopLogPolling();
                }
            }
            
            async validateUrl(url) {
                const formData = new FormData();
                formData.append('url', url);
                
                const response = await fetch('?action=validate', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error);
                }
            }
            
            async fetchContent(url) {
                const formData = new FormData();
                formData.append('url', url);
                
                const response = await fetch('?action=fetch', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error);
                }
            }
            
            async processStep(step) {
                const formData = new FormData();
                formData.append('step', step);
                
                const response = await fetch('?action=process', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error);
                }
                
                return result.result;
            }
            
            async getFinalResult() {
                // The final result is stored in session, we can get it via logs or a separate endpoint
                // For now, we'll use the last processing step result
                return document.getElementById('markdownOutput').value;
            }
            
            startLogPolling() {
                this.logPollingInterval = setInterval(() => {
                    this.updateLogs();
                }, 500);
            }
            
            stopLogPolling() {
                if (this.logPollingInterval) {
                    clearInterval(this.logPollingInterval);
                    this.logPollingInterval = null;
                }
            }
            
            async updateLogs() {
                try {
                    const response = await fetch('?action=logs');
                    const logs = await response.json();
                    this.displayLogs(logs);
                    this.updateProgressFromLogs(logs);
                } catch (error) {
                    console.error('Failed to fetch logs:', error);
                }
            }
            
            displayLogs(logs) {
                const logContainer = document.getElementById('logEntries');
                logContainer.innerHTML = '';
                
                logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.className = 'log-entry';
                    
                    const timestamp = new Date(log.timestamp * 1000).toLocaleTimeString();
                    const memoryMB = (log.memory_usage / 1024 / 1024).toFixed(2);
                    const peakMemoryMB = (log.memory_peak / 1024 / 1024).toFixed(2);
                    
                    logEntry.innerHTML = `
                        <div class="log-timestamp">[${timestamp}]</div>
                        <div class="log-step">${log.step.toUpperCase()}</div>
                        <div class="log-message">${log.message}</div>
                        <div class="log-stats">Time: ${(log.time_elapsed * 1000).toFixed(2)}ms | Memory: ${memoryMB}MB | Peak: ${peakMemoryMB}MB</div>
                    `;
                    
                    if (log.message.includes('Error:')) {
                        logEntry.classList.add('error-log');
                    }
                    
                    logContainer.appendChild(logEntry);
                });
                
                // Auto-scroll to bottom
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            updateProgress(percentage, message, currentStep = null) {
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                const progressPercentage = document.getElementById('progressPercentage');
                const progressTitle = document.getElementById('progressTitle');
                const progressSteps = document.querySelectorAll('.progress-step');
                
                if (progressFill) {
                    progressFill.style.width = percentage + '%';
                }
                if (progressPercentage) {
                    progressPercentage.textContent = Math.round(percentage) + '%';
                }
                if (progressText) {
                    progressText.textContent = message || 'Processing...';
                }
                if (progressTitle && currentStep) {
                    progressTitle.textContent = this.getStepTitle(currentStep);
                }
                
                // Update step indicators
                if (currentStep) {
                    this.updateStepIndicators(currentStep, percentage);
                }
            }
            
            getStepTitle(step) {
                const titles = {
                    'validate': 'Validating URL',
                    'fetch': 'Fetching Content',
                    'cleanup': 'Cleaning HTML',
                    'headers': 'Converting Headers',
                    'paragraphs': 'Processing Paragraphs',
                    'formatting': 'Applying Formatting',
                    'links': 'Converting Links',
                    'images': 'Processing Images',
                    'lists': 'Converting Lists',
                    'code': 'Processing Code',
                    'quotes': 'Converting Quotes',
                    'final': 'Final Processing'
                };
                return titles[step] || 'Processing';
            }
            
            updateStepIndicators(currentStep, percentage) {
                const steps = ['validate', 'fetch', 'cleanup', 'headers', 'paragraphs', 'formatting', 'links', 'images', 'lists', 'code', 'quotes', 'final'];
                const progressSteps = document.querySelectorAll('.progress-step');
                
                progressSteps.forEach((stepElement, index) => {
                    const stepName = stepElement.getAttribute('data-step');
                    const stepIndex = steps.indexOf(stepName);
                    const currentIndex = steps.indexOf(currentStep);
                    
                    stepElement.classList.remove('active', 'completed');
                    
                    if (stepIndex < currentIndex) {
                        stepElement.classList.add('completed');
                    } else if (stepIndex === currentIndex) {
                        stepElement.classList.add('active');
                    }
                });
            }
            
            updateProgressFromLogs(logs) {
                if (logs.length === 0) return;
                
                const latestLog = logs[logs.length - 1];
                const progress = latestLog.progress;
                
                const progressBar = document.getElementById('progressBar');
                const currentStep = document.getElementById('currentStep');
                const memoryUsage = document.getElementById('memoryUsage');
                
                progressBar.style.width = progress + '%';
                progressBar.textContent = progress + '%';
                
                currentStep.textContent = latestLog.message;
                
                const memoryMB = (latestLog.memory_usage / 1024 / 1024).toFixed(2);
                const peakMemoryMB = (latestLog.memory_peak / 1024 / 1024).toFixed(2);
                memoryUsage.textContent = `Memory Usage: ${memoryMB}MB | Peak: ${peakMemoryMB}MB`;
            }
            
            displayResult(markdown) {
                const output = document.getElementById('markdownOutput');
                const preview = document.getElementById('markdownPreview');
                
                output.value = markdown;
                
                // Simple markdown to HTML conversion for preview
                let html = markdown
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/gim, '<em>$1</em>')
                    .replace(/\[([^\]]+)\]\(([^\)]+)\)/gim, '<a href="$2">$1</a>')
                    .replace(/\n/gim, '<br>');
                
                preview.innerHTML = html;
            }
            
            updateUI(status) {
                const indicator = document.getElementById('statusIndicator');
                const convertBtn = document.getElementById('convertBtn');
                
                indicator.className = `status-indicator status-${status}`;
                
                switch (status) {
                    case 'processing':
                        convertBtn.disabled = true;
                        convertBtn.innerHTML = '<span class="status-indicator status-processing"></span>Processing...';
                        break;
                    case 'success':
                        convertBtn.disabled = false;
                        convertBtn.innerHTML = '<span class="status-indicator status-success"></span>Convert to Markdown';
                        break;
                    case 'error':
                        convertBtn.disabled = false;
                        convertBtn.innerHTML = '<span class="status-indicator status-error"></span>Convert to Markdown';
                        break;
                    default:
                        convertBtn.disabled = false;
                        convertBtn.innerHTML = '<span class="status-indicator status-idle"></span>Convert to Markdown';
                }
            }
            
            showConversionStatus() {
                document.getElementById('conversionStatus').style.display = 'block';
            }
            
            showError(message) {
                alert('Error: ' + message);
            }
            
            async downloadMarkdown() {
                try {
                    const response = await fetch('?action=download', {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'converted_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.md';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);
                    } else {
                        throw new Error('Download failed');
                    }
                } catch (error) {
                    alert('Download failed: ' + error.message);
                }
            }
            
            async clearLogs() {
                try {
                    await fetch('?action=clear', { method: 'POST' });
                    document.getElementById('logEntries').innerHTML = '';
                    document.getElementById('markdownOutput').value = '';
                    document.getElementById('markdownPreview').innerHTML = 'Preview will appear here after conversion...';
                    document.getElementById('conversionStatus').style.display = 'none';
                    document.getElementById('downloadBtn').disabled = true;
                    this.updateUI('idle');
                } catch (error) {
                    alert('Failed to clear logs: ' + error.message);
                }
            }
            
            toggleLogs() {
                const logContainer = document.getElementById('logContainer');
                const toggleBtn = document.getElementById('toggleLogsBtn');
                
                if (logContainer.style.display === 'none') {
                    logContainer.style.display = 'block';
                    toggleBtn.textContent = '🙈 Hide Logs';
                } else {
                    logContainer.style.display = 'none';
                    toggleBtn.textContent = '👁️ Show Logs';
                }
            }
            
            switchTab(tabName) {
                // Remove active class from all tabs and panes
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
                
                // Add active class to selected tab and pane
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
                document.getElementById(`${tabName}-tab`).classList.add('active');
            }
            
            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // Initialize the converter when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new UrlToMarkdownConverter();
        });
    </script>
</body>
</html>